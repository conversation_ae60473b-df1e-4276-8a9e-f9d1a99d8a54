from odoo.models import BaseModel
from odoo.exceptions import ValidationError, UserError
from odoo import fields, models, api, _

class UserAff(BaseModel):
    _inherit = 'res.users'

    def th_create_user(self, datas):
        import logging
        _logger = logging.getLogger(__name__)

        domain = [('login', '=', datas.login),('active', 'in', [False, True])]
        user = self.sudo().search(domain, limit=1)

        _logger.info(f"DEBUG th_create_user: login={datas.login}")
        _logger.info(f"DEBUG th_create_user: user found={bool(user)}")
        if user:
            _logger.info(f"DEBUG th_create_user: user.id={user.id}, user.active={user.active}, user.name={user.name}")

        if user:
            if user.active:
                _logger.info(f"DEBUG th_create_user: User already active, raising ValidationError")
                raise ValidationError(_('<PERSON><PERSON><PERSON> khoản đã được tạo!'))
            else:
                _logger.info(f"DEBUG th_create_user: Activating existing inactive user")
                user.sudo().write({'active': True})
                return user  # Return user đã được active
        else:
            # Tạo user mới nếu chưa tồn tại
            _logger.info(f"DEBUG th_create_user: Creating new user")
            partner = super(UserAff, self).create(datas.dict())
            partner.action_reset_password()
            return partner

    # def th_create_user(self, datas):
    #     domain = [('login', '=', datas.login)]
    #     partner = self.sudo().search(domain, limit=1)
    #     if not partner:
    #         partner = super(UserAff, self).create(datas.dict())
    #     return partner