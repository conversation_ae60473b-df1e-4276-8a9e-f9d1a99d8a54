from odoo.models import BaseModel
from odoo.exceptions import ValidationError, UserError
from odoo import fields, models, api, _

class UserAff(BaseModel):
    _inherit = 'res.users'

    def th_create_user(self, datas):
        try:
            domain = [('login', '=', datas.login),('active', 'in', [False, True])]
            user = self.sudo().search(domain, limit=1)

            print("DEBUG th_create_user: login=" + str(datas.login))
            print("DEBUG th_create_user: user found=" + str(bool(user)))
            if user:
                print("DEBUG th_create_user: user.id=" + str(user.id) + ", user.active=" + str(user.active) + ", user.name=" + str(user.name))

            if user:
                if user.active:
                    print("DEBUG th_create_user: User already active, raising ValidationError")
                    raise ValidationError(_('<PERSON><PERSON><PERSON> k<PERSON><PERSON>n đã được tạo!'))
                else:
                    print("DEBUG th_create_user: Activating existing inactive user")
                    user.sudo().write({'active': True})
                    return user  # Return user đã được active
            else:
                # Tạo user mới nếu chưa tồn tại
                print("DEBUG th_create_user: Creating new user")

                # Bước 1: Convert datas
                try:
                    user_data = datas.model_dump() if hasattr(datas, 'model_dump') else datas.dict()
                    print("DEBUG th_create_user: Convert data success")
                except Exception as e:
                    print("ERROR converting datas: " + str(e))
                    user_data = {
                        'name': datas.name,
                        'login': datas.login,
                        'email': datas.email,
                        'phone': datas.phone,
                    }
                    print("DEBUG th_create_user: Using manual data mapping")

                print("DEBUG th_create_user: user_data keys=" + str(list(user_data.keys())))

                # Bước 2: Create user
                try:
                    print("DEBUG th_create_user: About to create user")
                    partner = super(UserAff, self).create(user_data)
                    print("DEBUG th_create_user: User created successfully, id=" + str(partner.id))
                except Exception as e:
                    print("ERROR creating user: " + str(e))
                    raise

                # Bước 3: Reset password
                try:
                    print("DEBUG th_create_user: About to reset password")
                    partner.action_reset_password()
                    print("DEBUG th_create_user: Password reset successfully")
                except Exception as e:
                    print("ERROR resetting password: " + str(e))
                    # Không raise lỗi này vì user đã được tạo thành công

                return partner
        except Exception as e:
            print("ERROR in th_create_user: " + str(e))
            raise

    # def th_create_user(self, datas):
    #     domain = [('login', '=', datas.login)]
    #     partner = self.sudo().search(domain, limit=1)
    #     if not partner:
    #         partner = super(UserAff, self).create(datas.dict())
    #     return partner